"""
Report generation for the Online Register System
"""
import csv
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any
from pathlib import Path

from database.db_manager import DatabaseManager
from database.models import User, Session, UserRole
from utils.helpers import format_datetime, format_duration
import config

class ReportGenerator:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.reports_dir = Path(config.REPORTS_DIR)
        self.reports_dir.mkdir(exist_ok=True)
    
    def generate_user_activity_report(self, admin_user: User, days: int = 30) -> str:
        """Generate user activity report (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return "Access denied: Admin privileges required"
        
        # Get all users and their sessions
        users = self.db.get_all_users()
        cutoff_date = datetime.now() - timedelta(days=days)
        
        report_data = []
        for user in users:
            sessions = self.db.get_user_sessions(user.id, 1000)
            recent_sessions = [s for s in sessions if s.start_time and s.start_time >= cutoff_date]
            
            total_minutes = sum(s.duration_minutes or 0 for s in recent_sessions)
            total_hours = total_minutes / 60.0
            
            report_data.append({
                "username": user.username,
                "email": user.email,
                "role": user.role.value,
                "active": user.is_active,
                "last_login": format_datetime(user.last_login),
                "sessions_count": len(recent_sessions),
                "total_hours": round(total_hours, 2),
                "avg_session_duration": round(total_minutes / len(recent_sessions), 1) if recent_sessions else 0
            })
        
        # Sort by total hours descending
        report_data.sort(key=lambda x: x["total_hours"], reverse=True)
        
        # Generate report file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"user_activity_report_{timestamp}.csv"
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', newline='') as csvfile:
            fieldnames = ["username", "email", "role", "active", "last_login", 
                         "sessions_count", "total_hours", "avg_session_duration"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for row in report_data:
                writer.writerow(row)
        
        return f"Report generated: {filepath}"
    
    def generate_session_report(self, admin_user: User, days: int = 7) -> str:
        """Generate detailed session report (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return "Access denied: Admin privileges required"
        
        sessions = self.db.get_all_sessions(config.MAX_REPORT_RECORDS)
        cutoff_date = datetime.now() - timedelta(days=days)
        
        recent_sessions = [s for s in sessions if s.start_time and s.start_time >= cutoff_date]
        
        report_data = []
        for session in recent_sessions:
            user = self.db.get_user_by_id(session.user_id)
            username = user.username if user else "Unknown"
            
            report_data.append({
                "username": username,
                "start_time": format_datetime(session.start_time),
                "end_time": format_datetime(session.end_time),
                "duration_minutes": session.duration_minutes or 0,
                "location": session.location,
                "ip_address": session.ip_address,
                "status": session.status.value,
                "notes": session.notes
            })
        
        # Sort by start time descending
        report_data.sort(key=lambda x: x["start_time"], reverse=True)
        
        # Generate report file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"session_report_{timestamp}.csv"
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', newline='') as csvfile:
            fieldnames = ["username", "start_time", "end_time", "duration_minutes", 
                         "location", "ip_address", "status", "notes"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for row in report_data:
                writer.writerow(row)
        
        return f"Report generated: {filepath}"
    
    def generate_security_report(self, admin_user: User, days: int = 30) -> str:
        """Generate security events report (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return "Access denied: Admin privileges required"
        
        # Note: This would require implementing security log retrieval in DatabaseManager
        # For now, return a placeholder
        return "Security report generation not yet implemented"
    
    def get_dashboard_stats(self, admin_user: User) -> Dict[str, Any]:
        """Get dashboard statistics (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return {}
        
        users = self.db.get_all_users()
        sessions = self.db.get_all_sessions(1000)
        
        # Calculate statistics
        total_users = len(users)
        active_users = len([u for u in users if u.is_active])
        admin_users = len([u for u in users if u.role == UserRole.ADMIN])
        
        # Active sessions
        active_sessions = len([s for s in sessions if s.status.value == "active"])
        
        # Today's sessions
        today = datetime.now().date()
        today_sessions = [s for s in sessions if s.start_time and s.start_time.date() == today]
        
        # This week's total hours
        week_ago = datetime.now() - timedelta(days=7)
        week_sessions = [s for s in sessions if s.start_time and s.start_time >= week_ago]
        week_minutes = sum(s.duration_minutes or 0 for s in week_sessions)
        week_hours = week_minutes / 60.0
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "admin_users": admin_users,
            "active_sessions": active_sessions,
            "today_sessions": len(today_sessions),
            "week_total_hours": round(week_hours, 1),
            "total_sessions": len(sessions)
        }
    
    def export_user_data(self, admin_user: User, username: str) -> str:
        """Export all data for a specific user (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return "Access denied: Admin privileges required"
        
        user = self.db.get_user_by_username(username)
        if not user:
            return "User not found"
        
        sessions = self.db.get_user_sessions(user.id, 1000)
        
        user_data = {
            "user_info": {
                "username": user.username,
                "email": user.email,
                "role": user.role.value,
                "created_at": format_datetime(user.created_at),
                "last_login": format_datetime(user.last_login),
                "is_active": user.is_active,
                "totp_enabled": user.is_totp_enabled
            },
            "sessions": [
                {
                    "start_time": format_datetime(s.start_time),
                    "end_time": format_datetime(s.end_time),
                    "duration_minutes": s.duration_minutes,
                    "location": s.location,
                    "status": s.status.value,
                    "notes": s.notes
                }
                for s in sessions
            ]
        }
        
        # Generate export file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"user_export_{username}_{timestamp}.json"
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w') as jsonfile:
            json.dump(user_data, jsonfile, indent=2)
        
        return f"User data exported: {filepath}"
