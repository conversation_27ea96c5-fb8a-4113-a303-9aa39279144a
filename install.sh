#!/bin/bash

# WeThinkCode Student Register System Installation Script

echo "🎓 WeThinkCode Student Register System - Installation"
echo "===================================================="

# Check if running as root for system-wide installation
if [ "$EUID" -eq 0 ]; then
    INSTALL_DIR="/usr/local/bin"
    SYSTEM_INSTALL=true
    echo "📦 Installing system-wide to $INSTALL_DIR"
else
    INSTALL_DIR="$HOME/.local/bin"
    SYSTEM_INSTALL=false
    echo "📦 Installing for current user to $INSTALL_DIR"
    
    # Create user bin directory if it doesn't exist
    mkdir -p "$INSTALL_DIR"
    
    # Add to PATH if not already there
    if [[ ":$PATH:" != *":$INSTALL_DIR:"* ]]; then
        echo "📝 Adding $INSTALL_DIR to PATH in ~/.bashrc"
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
        echo "⚠️  Please run 'source ~/.bashrc' or restart your terminal after installation"
    fi
fi

# Get current directory
CURRENT_DIR=$(pwd)
PROJECT_NAME="wethinkcode-register"

# Check if we're in the right directory
if [ ! -f "$PROJECT_NAME" ]; then
    echo "❌ Error: $PROJECT_NAME executable not found in current directory"
    echo "Please run this script from the project directory"
    exit 1
fi

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Create application directory
APP_DIR="$INSTALL_DIR/wethinkcode-register-app"
echo "📁 Creating application directory: $APP_DIR"
mkdir -p "$APP_DIR"

# Copy all project files to application directory
echo "📋 Copying project files..."
cp -r . "$APP_DIR/"

# Create the executable script
EXECUTABLE_PATH="$INSTALL_DIR/$PROJECT_NAME"
echo "🔧 Creating executable: $EXECUTABLE_PATH"

cat > "$EXECUTABLE_PATH" << EOF
#!/usr/bin/env python3
"""
WeThinkCode Student Register System - System Executable
Installed from: $CURRENT_DIR
"""
import os
import sys
from pathlib import Path

# Set the application directory
APP_DIR = Path("$APP_DIR")
os.chdir(APP_DIR)
sys.path.insert(0, str(APP_DIR))

def main():
    try:
        from main import OnlineRegisterApp
        app = OnlineRegisterApp()
        app.run()
    except ImportError as e:
        print(f"❌ Failed to import application: {e}")
        print(f"📁 Application directory: {APP_DIR}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

# Make executable
chmod +x "$EXECUTABLE_PATH"

# Verify installation
if [ -f "$EXECUTABLE_PATH" ] && [ -x "$EXECUTABLE_PATH" ]; then
    echo "✅ Installation successful!"
    echo ""
    echo "📍 Executable installed at: $EXECUTABLE_PATH"
    echo "📁 Application files at: $APP_DIR"
    echo ""
    echo "🚀 Usage:"
    echo "  $PROJECT_NAME          # Run the application"
    echo ""
    echo "🔧 Management:"
    echo "  To uninstall: rm -rf $EXECUTABLE_PATH $APP_DIR"
    echo ""
    
    if [ "$SYSTEM_INSTALL" = false ]; then
        echo "⚠️  Note: Make sure $INSTALL_DIR is in your PATH"
        echo "   Add this to your ~/.bashrc if not already there:"
        echo "   export PATH=\"\$HOME/.local/bin:\$PATH\""
        echo ""
        echo "   Then run: source ~/.bashrc"
    fi
    
    echo "🎉 You can now run '$PROJECT_NAME' from anywhere!"
else
    echo "❌ Installation failed"
    exit 1
fi
