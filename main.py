#!/usr/bin/env python3
"""
Online Student Register System
A terminal-based authentication system with session tracking
"""
import sys
import logging
from datetime import datetime
from typing import Optional

# Import our modules
from database.db_manager import DatabaseManager
from database.models import User, UserRole
from auth.authenticator import Authenticator
from sessions.session_manager import SessionManager
from users.user_manager import UserManager
from reports.report_generator import ReportGenerator
from utils.helpers import *
from utils.location import LocationTracker
import config

class OnlineRegisterApp:
    def __init__(self):
        self.db = DatabaseManager()
        self.auth = Authenticator(self.db)
        self.session_manager = SessionManager(self.db)
        self.user_manager = UserManager(self.db, self.auth)
        self.report_generator = ReportGenerator(self.db)
        self.location_tracker = LocationTracker()
        
        self.current_user: Optional[User] = None
        self.running = True
        
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(config.LOG_FILE),
                logging.StreamHandler()
            ]
        )
        
        # Create default admin user
        self.user_manager.create_default_admin()
    
    def run(self):
        """Main application loop"""
        clear_screen()
        print_banner()
        
        print_info("Welcome to the Online Student Register System!")
        print_info("Please login or register to continue.")
        
        while self.running:
            try:
                if not self.current_user:
                    self.show_auth_menu()
                else:
                    self.show_main_menu()
            except KeyboardInterrupt:
                print_info("\nGoodbye!")
                self.running = False
            except Exception as e:
                print_error(f"An error occurred: {e}")
                logging.error(f"Application error: {e}")
                pause()
    
    def show_auth_menu(self):
        """Show authentication menu"""
        clear_screen()
        print_banner()

        print("\n" + "="*50)
        print("1. Login")
        print("2. Admin Setup (First Time Only)")
        print("3. Exit")
        print("="*50)
        print_info("Note: Student registration is done by administrators after login.")

        choice = get_input("Select an option (1-3): ")

        if choice == "1":
            self.login()
        elif choice == "2":
            self.admin_setup()
        elif choice == "3":
            self.running = False
        else:
            print_error("Invalid choice. Please try again.")
            pause()
    
    def login(self):
        """Handle user login"""
        clear_screen()
        print_banner()
        print("\n--- LOGIN ---")
        
        username = get_input("Username: ")
        password = get_secure_input("Password: ")
        
        # Check if user has TOTP enabled
        user = self.db.get_user_by_username(username)
        totp_code = ""
        if user and user.is_totp_enabled:
            totp_code = get_input("TOTP Code (from authenticator app): ")
        
        # Get location
        location_info = self.location_tracker.get_location_info()
        
        success, message, authenticated_user = self.auth.login(
            username, password, totp_code, 
            location_info["location"], location_info["ip"]
        )
        
        if success:
            self.current_user = authenticated_user
            print_success(message)
            print_info(f"Welcome back, {self.current_user.username}!")
        else:
            print_error(message)
        
        pause()
    
    def admin_setup(self):
        """Handle admin user setup for first time"""
        clear_screen()
        print_banner()
        print("\n--- ADMIN SETUP ---")
        print_warning("This is for initial admin account creation only!")

        # Check if any admin users already exist
        users = self.db.get_all_users()
        admin_users = [u for u in users if u.role == UserRole.ADMIN]

        if admin_users:
            print_error("Admin users already exist. Use the default admin account to login.")
            print_info("Default admin: username='admin', password='admin123'")
            pause()
            return

        username = get_input("Admin Username (min 3 characters): ")
        email = get_input("Admin Email (must end with @wethinkcode.co.za): ")
        password = get_secure_input("Password (min 6 characters): ")
        confirm_password = get_secure_input("Confirm password: ")

        if password != confirm_password:
            print_error("Passwords do not match!")
            pause()
            return

        success, message = self.auth.register_admin_user(username, email, password)

        if success:
            print_success(message)
            print_info("You can now login with your admin credentials.")
        else:
            print_error(message)

        pause()
    
    def show_main_menu(self):
        """Show main application menu"""
        clear_screen()
        print_banner()
        
        # Show current session status
        _, status_msg, _ = self.session_manager.get_session_status(self.current_user)
        print(f"\nUser: {self.current_user.username} ({self.current_user.role.value.title()})")
        print(f"Status: {status_msg}")
        
        print("\n" + "="*50)
        print("1. Start Session")
        print("2. End Session")
        print("3. View Session History")
        print("4. Profile Settings")
        print("5. Security Settings")
        
        if self.current_user.role == UserRole.ADMIN:
            print("\n--- ADMIN MENU ---")
            print("6. Register Student")
            print("7. Register Admin")
            print("8. User Management")
            print("9. Remove All Students")
            print("10. Generate Reports")
            print("11. System Dashboard")
        
        print("\n0. Logout")
        print("="*50)
        
        choice = get_input("Select an option: ")
        
        if choice == "1":
            self.start_session()
        elif choice == "2":
            self.end_session()
        elif choice == "3":
            self.view_session_history()
        elif choice == "4":
            self.profile_settings()
        elif choice == "5":
            self.security_settings()
        elif choice == "6" and self.current_user.role == UserRole.ADMIN:
            self.admin_register_student()
        elif choice == "7" and self.current_user.role == UserRole.ADMIN:
            self.admin_register_admin()
        elif choice == "8" and self.current_user.role == UserRole.ADMIN:
            self.admin_user_management()
        elif choice == "9" and self.current_user.role == UserRole.ADMIN:
            self.admin_remove_all_students()
        elif choice == "10" and self.current_user.role == UserRole.ADMIN:
            self.admin_reports()
        elif choice == "11" and self.current_user.role == UserRole.ADMIN:
            self.admin_dashboard()
        elif choice == "0":
            self.logout()
        else:
            print_error("Invalid choice. Please try again.")
            pause()
    
    def start_session(self):
        """Start a new session"""
        clear_screen()
        print_banner()
        print("\n--- START SESSION ---")
        
        # Check if already has active session
        has_session, status_msg, _ = self.session_manager.get_session_status(self.current_user)
        if has_session:
            print_warning(f"You already have an active session: {status_msg}")
            pause()
            return

        success, message = self.session_manager.start_session(self.current_user)
        
        if success:
            print_success(message)
        else:
            print_error(message)
        
        pause()
    
    def end_session(self):
        """End current session"""
        clear_screen()
        print_banner()
        print("\n--- END SESSION ---")
        
        success, message = self.session_manager.end_session(self.current_user)
        
        if success:
            print_success(message)
        else:
            print_error(message)
        
        pause()
    
    def logout(self):
        """Logout current user"""
        # End any active session
        active_session = self.session_manager.get_current_session(self.current_user)
        if active_session:
            if confirm_action("You have an active session. End it before logout?"):
                self.session_manager.end_session(self.current_user)

        print_info(f"Goodbye, {self.current_user.username}!")
        self.current_user = None
        pause()

    def view_session_history(self):
        """View user's session history"""
        clear_screen()
        print_banner()
        print("\n--- SESSION HISTORY ---")

        sessions = self.session_manager.get_session_history(self.current_user, 10)

        if not sessions:
            print_info("No session history found.")
            pause()
            return

        print_table_header(["Start Time", "End Time", "Duration", "Location", "Status"])

        for session in sessions:
            start_time = format_datetime(session.start_time)[:16] if session.start_time else "N/A"
            end_time = format_datetime(session.end_time)[:16] if session.end_time else "Active"
            duration = format_duration(session.duration_minutes)
            location = truncate_string(session.location or "Unknown")
            status = session.status.value.title()

            print_table_row([start_time, end_time, duration, location, status])

        # Show summary
        total_hours = self.session_manager.get_total_hours_week(self.current_user)
        print(f"\nTotal hours this week: {total_hours:.1f}h")

        pause()

    def profile_settings(self):
        """User profile settings"""
        clear_screen()
        print_banner()
        print("\n--- PROFILE SETTINGS ---")

        profile = self.user_manager.get_user_profile(self.current_user)

        print(f"Username: {profile['username']}")
        print(f"Email: {profile['email']}")
        print(f"Role: {profile['role']}")
        print(f"Member since: {format_datetime(profile['created_at'])}")
        print(f"Last login: {format_datetime(profile['last_login'])}")
        print(f"Total sessions: {profile['total_sessions']}")
        print(f"Total hours: {profile['total_hours']:.1f}h")
        print(f"TOTP enabled: {'Yes' if profile['totp_enabled'] else 'No'}")

        print("\n1. Update Email")
        print("2. Change Password")
        print("0. Back")

        choice = get_input("Select an option: ")

        if choice == "1":
            self.update_email()
        elif choice == "2":
            self.change_password()
        elif choice == "0":
            return
        else:
            print_error("Invalid choice.")
            pause()

    def update_email(self):
        """Update user email"""
        new_email = get_input("Enter new email: ")
        success, message = self.user_manager.update_user_email(self.current_user, new_email)

        if success:
            print_success(message)
        else:
            print_error(message)

        pause()

    def change_password(self):
        """Change user password"""
        current_password = get_secure_input("Current password: ")
        new_password = get_secure_input("New password: ")
        confirm_password = get_secure_input("Confirm new password: ")

        if new_password != confirm_password:
            print_error("Passwords do not match!")
            pause()
            return

        success, message = self.auth.change_password(self.current_user, current_password, new_password)

        if success:
            print_success(message)
        else:
            print_error(message)

        pause()

    def security_settings(self):
        """Security settings menu"""
        clear_screen()
        print_banner()
        print("\n--- SECURITY SETTINGS ---")

        print(f"TOTP Status: {'Enabled' if self.current_user.is_totp_enabled else 'Disabled'}")

        print("\n1. Setup/Enable TOTP")
        print("2. Disable TOTP")
        print("0. Back")

        choice = get_input("Select an option: ")

        if choice == "1":
            self.setup_totp()
        elif choice == "2":
            self.disable_totp()
        elif choice == "0":
            return
        else:
            print_error("Invalid choice.")
            pause()

    def setup_totp(self):
        """Setup TOTP authentication"""
        clear_screen()
        print_banner()
        print("\n--- SETUP TOTP ---")

        if self.current_user.is_totp_enabled:
            print_warning("TOTP is already enabled for your account.")
            pause()
            return

        secret, qr_url = self.auth.setup_totp(self.current_user)

        print("Scan this QR code with your authenticator app:")
        print(self.auth.totp_manager.generate_qr_code_ascii(self.current_user.username, secret))

        print(f"\nOr enter this secret manually: {secret}")
        print(f"QR URL: {qr_url}")

        print("\nAfter setting up your authenticator app, enter the 6-digit code to verify:")
        totp_code = get_input("TOTP Code: ")

        success, message = self.auth.enable_totp(self.current_user, totp_code)

        if success:
            print_success(message)
        else:
            print_error(message)

        pause()

    def disable_totp(self):
        """Disable TOTP authentication"""
        if not self.current_user.is_totp_enabled:
            print_warning("TOTP is not enabled for your account.")
            pause()
            return

        password = get_secure_input("Enter your password to disable TOTP: ")
        success, message = self.auth.disable_totp(self.current_user, password)

        if success:
            print_success(message)
        else:
            print_error(message)

        pause()

    def admin_register_student(self):
        """Admin function to register students"""
        clear_screen()
        print_banner()
        print("\n--- REGISTER STUDENT ---")

        print_info("Register students using their WeThinkCode email addresses")
        print_info("Temporary passwords will be sent via email")

        student_email = get_input("Student Email (@student.wethinkcode.co.za): ")

        if not student_email:
            print_error("Email is required")
            pause()
            return

        # Confirm registration
        if not confirm_action(f"Register student with email {student_email}?"):
            print_info("Registration cancelled")
            pause()
            return

        print_info("Registering student...")
        success, message, temp_password = self.auth.register_student_by_admin(
            self.current_user, student_email
        )

        if success:
            print_success(message)
            if temp_password:
                print_info(f"Temporary password: {temp_password}")
                print_warning("Make sure the student changes this password on first login!")
        else:
            print_error(message)

        pause()

    def admin_register_admin(self):
        """Admin function to register other administrators"""
        clear_screen()
        print_banner()
        print("\n--- REGISTER ADMIN ---")

        print_info("Register new administrators using WeThinkCode email addresses")
        print_warning("Admin accounts have full system access!")

        username = get_input("Admin Username (min 3 characters): ")
        email = get_input("Admin Email (@wethinkcode.co.za): ")
        password = get_secure_input("Password (min 6 characters): ")
        confirm_password = get_secure_input("Confirm password: ")

        if not username or not email or not password:
            print_error("All fields are required")
            pause()
            return

        if password != confirm_password:
            print_error("Passwords do not match!")
            pause()
            return

        # Confirm registration
        if not confirm_action(f"Register admin with username '{username}' and email '{email}'?"):
            print_info("Registration cancelled")
            pause()
            return

        print_info("Registering admin...")
        success, message = self.auth.register_admin_user(username, email, password)

        if success:
            print_success(message)
            print_info(f"Admin '{username}' can now login with their credentials")
        else:
            print_error(message)

        pause()

    def admin_remove_all_students(self):
        """Admin function to remove all student accounts"""
        clear_screen()
        print_banner()
        print("\n--- REMOVE ALL STUDENTS ---")

        # Get all users
        users = self.db.get_all_users()
        students = [u for u in users if u.role == UserRole.STUDENT]

        if not students:
            print_info("No student accounts found in the system")
            pause()
            return

        print_warning(f"This will permanently delete {len(students)} student accounts:")
        for student in students[:10]:  # Show first 10
            print(f"  • {student.username} ({student.email})")

        if len(students) > 10:
            print(f"  ... and {len(students) - 10} more")

        print_error("THIS ACTION CANNOT BE UNDONE!")

        # Double confirmation
        if not confirm_action("Are you sure you want to delete ALL student accounts?"):
            print_info("Operation cancelled")
            pause()
            return

        print_warning("Type 'DELETE ALL STUDENTS' to confirm:")
        confirmation = get_input("Confirmation: ")

        if confirmation != "DELETE ALL STUDENTS":
            print_error("Confirmation text does not match. Operation cancelled.")
            pause()
            return

        # Remove all students
        print_info("Removing student accounts...")
        removed_count = 0

        for student in students:
            try:
                # First remove all sessions for this student
                sessions = self.db.get_user_sessions(student.id, 1000)
                for session in sessions:
                    # Note: We would need to add a delete_session method to DatabaseManager
                    pass

                # Remove the user (we need to add this method)
                success = self._delete_user(student.id)
                if success:
                    removed_count += 1
                    print(f"  ✅ Removed: {student.username}")
                else:
                    print(f"  ❌ Failed to remove: {student.username}")
            except Exception as e:
                print(f"  ❌ Error removing {student.username}: {e}")

        print_success(f"Successfully removed {removed_count} student accounts")
        print_info(f"Remaining users: {len(self.db.get_all_users())}")

        pause()

    def _delete_user(self, user_id: int) -> bool:
        """Helper method to delete a user (we need to implement this in DatabaseManager)"""
        try:
            with self.db.get_connection() as conn:
                # Delete user sessions first
                conn.execute("DELETE FROM sessions WHERE user_id = ?", (user_id,))
                # Delete security logs
                conn.execute("DELETE FROM security_logs WHERE user_id = ?", (user_id,))
                # Delete password resets
                conn.execute("DELETE FROM password_resets WHERE user_id = ?", (user_id,))
                # Delete user
                conn.execute("DELETE FROM users WHERE id = ?", (user_id,))
                conn.commit()
                return True
        except Exception as e:
            print(f"Database error: {e}")
            return False

    def admin_user_management(self):
        """Admin user management menu"""
        clear_screen()
        print_banner()
        print("\n--- USER MANAGEMENT ---")

        print("1. List All Users")
        print("2. Activate/Deactivate User")
        print("3. Reset User Password")
        print("4. Promote/Demote User")
        print("5. User Statistics")
        print("0. Back")

        choice = get_input("Select an option: ")

        if choice == "1":
            self.admin_list_users()
        elif choice == "2":
            self.admin_toggle_user_status()
        elif choice == "3":
            self.admin_reset_password()
        elif choice == "4":
            self.admin_change_role()
        elif choice == "5":
            self.admin_user_stats()
        elif choice == "0":
            return
        else:
            print_error("Invalid choice.")
            pause()

    def admin_list_users(self):
        """List all users"""
        clear_screen()
        print_banner()
        print("\n--- ALL USERS ---")

        users = self.user_manager.list_all_users(self.current_user)

        if not users:
            print_info("No users found.")
            pause()
            return

        print_table_header(["Username", "Email", "Role", "Status", "Last Login"])

        for user in users:
            username = truncate_string(user.username)
            email = truncate_string(user.email)
            role = user.role.value.title()
            status = "Active" if user.is_active else "Inactive"
            last_login = format_datetime(user.last_login)[:16] if user.last_login else "Never"

            print_table_row([username, email, role, status, last_login])

        pause()

    def admin_toggle_user_status(self):
        """Activate or deactivate user"""
        username = get_input("Enter username: ")
        action = get_input("Enter action (activate/deactivate): ").lower()

        if action == "activate":
            success, message = self.user_manager.activate_user(self.current_user, username)
        elif action == "deactivate":
            success, message = self.user_manager.deactivate_user(self.current_user, username)
        else:
            print_error("Invalid action. Use 'activate' or 'deactivate'.")
            pause()
            return

        if success:
            print_success(message)
        else:
            print_error(message)

        pause()

    def admin_reset_password(self):
        """Reset user password"""
        username = get_input("Enter username: ")
        new_password = get_secure_input("Enter new password: ")

        success, message = self.user_manager.reset_user_password(
            self.current_user, username, new_password
        )

        if success:
            print_success(message)
        else:
            print_error(message)

        pause()

    def admin_change_role(self):
        """Change user role"""
        username = get_input("Enter username: ")
        action = get_input("Enter action (promote/demote): ").lower()

        if action == "promote":
            success, message = self.user_manager.promote_to_admin(self.current_user, username)
        elif action == "demote":
            success, message = self.user_manager.demote_from_admin(self.current_user, username)
        else:
            print_error("Invalid action. Use 'promote' or 'demote'.")
            pause()
            return

        if success:
            print_success(message)
        else:
            print_error(message)

        pause()

    def admin_user_stats(self):
        """Show user statistics"""
        clear_screen()
        print_banner()
        print("\n--- USER STATISTICS ---")

        stats = self.user_manager.get_user_statistics(self.current_user)

        print(f"Total Users: {stats['total_users']}")
        print(f"Active Users: {stats['active_users']}")
        print(f"Inactive Users: {stats['inactive_users']}")
        print(f"Admin Users: {stats['admin_users']}")
        print(f"Student Users: {stats['student_users']}")

        pause()

    def admin_reports(self):
        """Admin reports menu"""
        clear_screen()
        print_banner()
        print("\n--- REPORTS ---")

        print("1. User Activity Report")
        print("2. Session Report")
        print("3. Export User Data")
        print("0. Back")

        choice = get_input("Select an option: ")

        if choice == "1":
            self.admin_user_activity_report()
        elif choice == "2":
            self.admin_session_report()
        elif choice == "3":
            self.admin_export_user_data()
        elif choice == "0":
            return
        else:
            print_error("Invalid choice.")
            pause()

    def admin_user_activity_report(self):
        """Generate user activity report"""
        days = get_input("Enter number of days (default 30): ")
        try:
            days = int(days) if days else 30
        except ValueError:
            days = 30

        print_info("Generating report...")
        result = self.report_generator.generate_user_activity_report(self.current_user, days)
        print_success(result)
        pause()

    def admin_session_report(self):
        """Generate session report"""
        days = get_input("Enter number of days (default 7): ")
        try:
            days = int(days) if days else 7
        except ValueError:
            days = 7

        print_info("Generating report...")
        result = self.report_generator.generate_session_report(self.current_user, days)
        print_success(result)
        pause()

    def admin_export_user_data(self):
        """Export user data"""
        username = get_input("Enter username to export: ")

        print_info("Exporting user data...")
        result = self.report_generator.export_user_data(self.current_user, username)
        print_success(result)
        pause()

    def admin_dashboard(self):
        """Admin dashboard"""
        clear_screen()
        print_banner()
        print("\n--- SYSTEM DASHBOARD ---")

        stats = self.report_generator.get_dashboard_stats(self.current_user)

        print(f"Total Users: {stats['total_users']}")
        print(f"Active Users: {stats['active_users']}")
        print(f"Admin Users: {stats['admin_users']}")
        print(f"Active Sessions: {stats['active_sessions']}")
        print(f"Today's Sessions: {stats['today_sessions']}")
        print(f"Week Total Hours: {stats['week_total_hours']}h")
        print(f"Total Sessions: {stats['total_sessions']}")

        pause()

if __name__ == "__main__":
    app = OnlineRegisterApp()
    app.run()
