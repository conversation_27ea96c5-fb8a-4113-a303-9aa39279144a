"""
Data models for the Online Register System
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List
from enum import Enum

class UserRole(Enum):
    STUDENT = "student"
    ADMIN = "admin"

class SessionStatus(Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    INTERRUPTED = "interrupted"

@dataclass
class User:
    id: Optional[int] = None
    username: str = ""
    email: str = ""
    password_hash: str = ""
    role: UserRole = UserRole.STUDENT
    totp_secret: Optional[str] = None
    is_totp_enabled: bool = False
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    is_active: bool = True

@dataclass
class Session:
    id: Optional[int] = None
    user_id: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    location: str = ""
    ip_address: str = ""
    status: SessionStatus = SessionStatus.ACTIVE
    notes: str = ""

@dataclass
class SecurityLog:
    id: Optional[int] = None
    user_id: Optional[int] = None
    username: str = ""
    action: str = ""
    ip_address: str = ""
    location: str = ""
    timestamp: Optional[datetime] = None
    success: bool = True
    details: str = ""

@dataclass
class PasswordReset:
    id: Optional[int] = None
    user_id: int = 0
    token: str = ""
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    used: bool = False
