"""
Database manager for the Online Register System
"""
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Tuple
from pathlib import Path

from .models import User, Session, SecurityLog, PasswordReset, UserRole, SessionStatus
import config

class DatabaseManager:
    def __init__(self, db_path: str = config.DATABASE_PATH):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with row factory"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database tables"""
        with self.get_connection() as conn:
            # Users table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'student',
                    totp_secret TEXT,
                    is_totp_enabled BOOLEAN DEFAULT FALSE,
                    failed_login_attempts INTEGER DEFAULT 0,
                    locked_until TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """)
            
            # Sessions table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    duration_minutes INTEGER,
                    location TEXT,
                    ip_address TEXT,
                    status TEXT DEFAULT 'active',
                    notes TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # Security logs table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS security_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    username TEXT,
                    action TEXT NOT NULL,
                    ip_address TEXT,
                    location TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN DEFAULT TRUE,
                    details TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # Password reset table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS password_resets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    token TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    used BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            conn.commit()
    
    def create_user(self, user: User) -> Optional[int]:
        """Create a new user"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO users (username, email, password_hash, role, totp_secret, is_totp_enabled)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (user.username, user.email, user.password_hash, user.role.value, 
                     user.totp_secret, user.is_totp_enabled))
                return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        with self.get_connection() as conn:
            row = conn.execute("SELECT * FROM users WHERE username = ?", (username,)).fetchone()
            if row:
                return self._row_to_user(row)
        return None
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        with self.get_connection() as conn:
            row = conn.execute("SELECT * FROM users WHERE id = ?", (user_id,)).fetchone()
            if row:
                return self._row_to_user(row)
        return None
    
    def update_user(self, user: User) -> bool:
        """Update user information"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    UPDATE users SET email = ?, password_hash = ?, role = ?, 
                           totp_secret = ?, is_totp_enabled = ?, failed_login_attempts = ?,
                           locked_until = ?, last_login = ?, is_active = ?
                    WHERE id = ?
                """, (user.email, user.password_hash, user.role.value, user.totp_secret,
                     user.is_totp_enabled, user.failed_login_attempts, user.locked_until,
                     user.last_login, user.is_active, user.id))
                return True
        except Exception as e:
            logging.error(f"Error updating user: {e}")
            return False
    
    def _row_to_user(self, row) -> User:
        """Convert database row to User object"""
        return User(
            id=row['id'],
            username=row['username'],
            email=row['email'],
            password_hash=row['password_hash'],
            role=UserRole(row['role']),
            totp_secret=row['totp_secret'],
            is_totp_enabled=bool(row['is_totp_enabled']),
            failed_login_attempts=row['failed_login_attempts'],
            locked_until=datetime.fromisoformat(row['locked_until']) if row['locked_until'] else None,
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            last_login=datetime.fromisoformat(row['last_login']) if row['last_login'] else None,
            is_active=bool(row['is_active'])
        )

    def create_session(self, session: Session) -> Optional[int]:
        """Create a new session"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO sessions (user_id, location, ip_address, status, notes)
                    VALUES (?, ?, ?, ?, ?)
                """, (session.user_id, session.location, session.ip_address,
                     session.status.value, session.notes))
                return cursor.lastrowid
        except Exception as e:
            logging.error(f"Error creating session: {e}")
            return None

    def end_session(self, session_id: int) -> bool:
        """End an active session"""
        try:
            with self.get_connection() as conn:
                # Get session start time
                row = conn.execute("SELECT start_time FROM sessions WHERE id = ?", (session_id,)).fetchone()
                if not row:
                    return False

                start_time = datetime.fromisoformat(row['start_time'])
                end_time = datetime.now()
                duration = int((end_time - start_time).total_seconds() / 60)

                conn.execute("""
                    UPDATE sessions SET end_time = ?, duration_minutes = ?, status = ?
                    WHERE id = ?
                """, (end_time, duration, SessionStatus.COMPLETED.value, session_id))
                return True
        except Exception as e:
            logging.error(f"Error ending session: {e}")
            return False

    def get_active_session(self, user_id: int) -> Optional[Session]:
        """Get active session for user"""
        with self.get_connection() as conn:
            row = conn.execute("""
                SELECT * FROM sessions WHERE user_id = ? AND status = 'active'
                ORDER BY start_time DESC LIMIT 1
            """, (user_id,)).fetchone()
            if row:
                return self._row_to_session(row)
        return None

    def get_user_sessions(self, user_id: int, limit: int = 50) -> List[Session]:
        """Get user session history"""
        with self.get_connection() as conn:
            rows = conn.execute("""
                SELECT * FROM sessions WHERE user_id = ?
                ORDER BY start_time DESC LIMIT ?
            """, (user_id, limit)).fetchall()
            return [self._row_to_session(row) for row in rows]

    def _row_to_session(self, row) -> Session:
        """Convert database row to Session object"""
        return Session(
            id=row['id'],
            user_id=row['user_id'],
            start_time=datetime.fromisoformat(row['start_time']) if row['start_time'] else None,
            end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
            duration_minutes=row['duration_minutes'],
            location=row['location'] or "",
            ip_address=row['ip_address'] or "",
            status=SessionStatus(row['status']),
            notes=row['notes'] or ""
        )

    def log_security_event(self, log: SecurityLog) -> bool:
        """Log security event"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO security_logs (user_id, username, action, ip_address,
                                             location, success, details)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (log.user_id, log.username, log.action, log.ip_address,
                     log.location, log.success, log.details))
                return True
        except Exception as e:
            logging.error(f"Error logging security event: {e}")
            return False

    def get_all_users(self) -> List[User]:
        """Get all users (admin function)"""
        with self.get_connection() as conn:
            rows = conn.execute("SELECT * FROM users ORDER BY username").fetchall()
            return [self._row_to_user(row) for row in rows]

    def get_all_sessions(self, limit: int = 100) -> List[Session]:
        """Get all sessions (admin function)"""
        with self.get_connection() as conn:
            rows = conn.execute("""
                SELECT * FROM sessions ORDER BY start_time DESC LIMIT ?
            """, (limit,)).fetchall()
            return [self._row_to_session(row) for row in rows]
