#!/usr/bin/env python3
"""
WeThinkCode Student Register System - Executable Script
Can be moved to /bin for system-wide access
"""
import os
import sys
import subprocess
from pathlib import Path

# Get the directory where this script is located
SCRIPT_DIR = Path(__file__).parent.absolute()

# Add the project directory to Python path
sys.path.insert(0, str(SCRIPT_DIR))

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import bcrypt
        import pyotp
        import qrcode
        import requests
        import tabulate
        import colorama
        import cryptography
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e.name}")
        return False

def install_dependencies():
    """Install required dependencies"""
    requirements_file = SCRIPT_DIR / "requirements.txt"
    if requirements_file.exists():
        print("📦 Installing dependencies...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
            print("✅ Dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False
    return True

def show_banner():
    """Show application banner"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║              WeThinkCode Student Register System              ║
║                     Executable Version                       ║
╚══════════════════════════════════════════════════════════════╝
    """)

def main():
    """Main entry point"""
    show_banner()
    
    # Change to script directory to ensure relative imports work
    os.chdir(SCRIPT_DIR)
    
    # Check dependencies
    if not check_dependencies():
        print("🔧 Attempting to install missing dependencies...")
        if not install_dependencies():
            print("❌ Please install dependencies manually:")
            print("pip install -r requirements.txt")
            sys.exit(1)
    
    # Import and run the main application
    try:
        from main import OnlineRegisterApp
        app = OnlineRegisterApp()
        app.run()
    except ImportError as e:
        print(f"❌ Failed to import application: {e}")
        print(f"📁 Make sure you're running from the correct directory: {SCRIPT_DIR}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
