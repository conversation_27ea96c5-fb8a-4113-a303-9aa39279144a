#!/usr/bin/env python3
"""
Demo script for the WeThinkCode Student Register System
Showcases all major features including admin registration, location tracking, and session management
"""
import time
from database.db_manager import DatabaseManager
from auth.authenticator import Authenticator
from sessions.session_manager import SessionManager
from users.user_manager import UserManager
from utils.location import LocationTracker
from utils.helpers import *

def demo_banner():
    """Display demo banner"""
    print("\n" + "="*70)
    print("🎓 WETHINKCODE STUDENT REGISTER SYSTEM DEMO")
    print("="*70)
    print("This demo showcases the complete functionality of the system:")
    print("• Admin-only student registration with email integration")
    print("• Location permission and automatic detection")
    print("• Session tracking with enhanced location features")
    print("• Security features and user management")
    print("="*70)

def demo_admin_registration():
    """Demo admin registration of students"""
    print("\n📋 DEMO: Admin Student Registration")
    print("-" * 40)
    
    # Initialize system
    db = DatabaseManager()
    auth = Authenticator(db)
    user_manager = UserManager(db, auth)
    
    # Create admin user
    admin_user = db.get_user_by_username("admin")
    if not admin_user:
        user_manager.create_default_admin()
        admin_user = db.get_user_by_username("admin")
    
    print(f"✅ Admin user ready: {admin_user.username}")
    
    # Register sample students
    sample_students = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    for email in sample_students:
        success, message, temp_password = auth.register_student_by_admin(admin_user, email)
        if success:
            print(f"✅ Registered: {email.split('@')[0]} | Password: {temp_password}")
        else:
            print(f"❌ Failed: {email} - {message}")
    
    print(f"\n📊 Total users in system: {len(db.get_all_users())}")

def demo_location_tracking():
    """Demo location tracking features"""
    print("\n📍 DEMO: Location Tracking System")
    print("-" * 40)
    
    tracker = LocationTracker()
    
    print("🔍 Testing location detection methods...")
    
    # Test basic location info
    location_info = tracker.get_location_info()
    print(f"📍 Basic Location: {location_info['location']}")
    print(f"🌐 IP Address: {location_info['ip']}")
    
    # Test precise location
    print("\n🎯 Testing precise location detection...")
    precise_location = tracker.get_precise_location()
    print(f"📍 Precise Location: {precise_location}")

def demo_session_management():
    """Demo session management"""
    print("\n⏱️  DEMO: Session Management")
    print("-" * 40)
    
    db = DatabaseManager()
    auth = Authenticator(db)
    session_manager = SessionManager(db)
    
    # Get a student user
    student = db.get_user_by_username("john.doe")
    if not student:
        print("❌ No student user found. Run admin registration demo first.")
        return
    
    print(f"👤 Testing with student: {student.username}")
    
    # Start session
    print("\n🟢 Starting session...")
    success, message = session_manager.start_session(student)
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
    
    # Check session status
    has_session, status_msg, session = session_manager.get_session_status(student)
    if has_session:
        print(f"📊 Session Status: {status_msg}")
    
    # Simulate some time passing
    print("⏳ Simulating 5 seconds of study time...")
    time.sleep(5)
    
    # End session
    print("\n🔴 Ending session...")
    success, message = session_manager.end_session(student)
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
    
    # Show session history
    sessions = session_manager.get_session_history(student, 5)
    print(f"\n📈 Recent sessions: {len(sessions)}")
    for session in sessions[:3]:  # Show last 3
        duration = format_duration(session.duration_minutes)
        location = session.location or "Unknown"
        print(f"  • {duration} at {location}")

def demo_security_features():
    """Demo security features"""
    print("\n🔒 DEMO: Security Features")
    print("-" * 40)
    
    db = DatabaseManager()
    auth = Authenticator(db)
    
    # Test password hashing
    test_password = "test123"
    hashed = auth.hash_password(test_password)
    verified = auth.verify_password(test_password, hashed)
    
    print(f"🔐 Password Hashing: {'✅ Working' if verified else '❌ Failed'}")
    
    # Test TOTP setup (simulation)
    student = db.get_user_by_username("john.doe")
    if student:
        secret, qr_url = auth.setup_totp(student)
        print(f"📱 TOTP Secret Generated: {secret[:8]}...")
        print(f"📱 QR URL Generated: {'✅ Yes' if qr_url else '❌ No'}")

def demo_admin_features():
    """Demo admin features"""
    print("\n👑 DEMO: Admin Features")
    print("-" * 40)
    
    db = DatabaseManager()
    user_manager = UserManager(db, None)
    
    admin_user = db.get_user_by_username("admin")
    if not admin_user:
        print("❌ No admin user found")
        return
    
    # User statistics
    stats = user_manager.get_user_statistics(admin_user)
    print("📊 System Statistics:")
    for key, value in stats.items():
        print(f"  • {key.replace('_', ' ').title()}: {value}")
    
    # List users
    users = user_manager.list_all_users(admin_user)
    print(f"\n👥 Total Users: {len(users)}")
    for user in users[:5]:  # Show first 5
        role = user.role.value.title()
        status = "Active" if user.is_active else "Inactive"
        print(f"  • {user.username} ({role}) - {status}")

def main():
    """Run complete demo"""
    demo_banner()
    
    try:
        # Run all demos
        demo_admin_registration()
        demo_location_tracking()
        demo_session_management()
        demo_security_features()
        demo_admin_features()
        
        print("\n" + "="*70)
        print("🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("="*70)
        print("The WeThinkCode Student Register System is ready for use.")
        print("Run 'python3 main.py' to start the interactive application.")
        print("="*70)
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        print("Please check your installation and try again.")

if __name__ == "__main__":
    main()
