"""
Email service for sending temporary passwords and notifications
"""
import secrets
import string
from typing import Tuple
import config

class EmailService:
    def __init__(self):
        # For demo purposes, we'll simulate email sending
        # In production, you would configure actual SMTP settings
        pass
    
    def generate_temporary_password(self) -> str:
        """Generate a secure temporary password"""
        characters = string.ascii_letters + string.digits + "!@#$%"
        return ''.join(secrets.choice(characters) for _ in range(config.TEMP_PASSWORD_LENGTH))
    
    def validate_wethinkcode_email(self, email: str) -> Tuple[bool, str]:
        """Validate if email is a valid WeThinkCode email"""
        if not email:
            return False, "Email is required"
        
        if email.endswith(config.WETHINKCODE_EMAIL_DOMAIN):
            return True, "Valid student email"
        elif email.endswith(config.ADMIN_EMAIL_DOMAIN):
            return True, "Valid admin email"
        else:
            return False, f"Email must end with {config.WETHINKCODE_EMAIL_DOMAIN} or {config.ADMIN_EMAIL_DOMAIN}"
    
    def send_temporary_password(self, student_email: str, temp_password: str, 
                              admin_name: str) -> Tuple[bool, str]:
        """Send temporary password to student email"""
        try:
            # For demo purposes, we'll just print the email content
            # In production, you would actually send the email
            
            subject = "WeThinkCode Student Register - Temporary Password"
            
            body = f"""
Dear Student,

Your account has been created in the WeThinkCode Student Register System by {admin_name}.

Login Details:
- Username: {student_email.split('@')[0]}
- Temporary Password: {temp_password}

Please log in and change your password immediately for security.

System URL: [Your system URL here]

Best regards,
WeThinkCode Student Register System
            """
            
            # Simulate email sending
            print(f"\n{'='*60}")
            print("EMAIL SIMULATION - TEMPORARY PASSWORD")
            print(f"{'='*60}")
            print(f"To: {student_email}")
            print(f"Subject: {subject}")
            print(f"Body:\n{body}")
            print(f"{'='*60}\n")
            
            return True, f"Temporary password sent to {student_email}"
            
        except Exception as e:
            return False, f"Failed to send email: {str(e)}"
    
    def send_password_reset(self, email: str, reset_token: str) -> Tuple[bool, str]:
        """Send password reset email"""
        try:
            subject = "WeThinkCode Student Register - Password Reset"
            
            body = f"""
Dear User,

You have requested a password reset for your WeThinkCode Student Register account.

Reset Token: {reset_token}

Please use this token to reset your password within 24 hours.

If you did not request this reset, please ignore this email.

Best regards,
WeThinkCode Student Register System
            """
            
            # Simulate email sending
            print(f"\n{'='*60}")
            print("EMAIL SIMULATION - PASSWORD RESET")
            print(f"{'='*60}")
            print(f"To: {email}")
            print(f"Subject: {subject}")
            print(f"Body:\n{body}")
            print(f"{'='*60}\n")
            
            return True, f"Password reset email sent to {email}"
            
        except Exception as e:
            return False, f"Failed to send reset email: {str(e)}"
    
    def extract_username_from_email(self, email: str) -> str:
        """Extract username from WeThinkCode email"""
        return email.split('@')[0]
    
    def is_admin_email(self, email: str) -> bool:
        """Check if email is an admin email"""
        return email.endswith(config.ADMIN_EMAIL_DOMAIN)
    
    def is_student_email(self, email: str) -> bool:
        """Check if email is a student email"""
        return email.endswith(config.WETHINKCODE_EMAIL_DOMAIN)
