"""
Utility functions for the Online Register System
"""
import os
import sys
import getpass
import socket
from datetime import datetime
from typing import Optional
import config

def clear_screen():
    """Clear terminal screen"""
    if config.CLEAR_SCREEN:
        os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    """Print application banner"""
    if config.SHOW_BANNER:
        banner = f"""
╔══════════════════════════════════════════════════════════════╗
║                    {config.APP_NAME}                    ║
║                        Version {config.APP_VERSION}                        ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)

def get_secure_input(prompt: str) -> str:
    """Get secure input (hidden for passwords)"""
    return getpass.getpass(prompt)

def get_input(prompt: str) -> str:
    """Get regular input with prompt"""
    return input(prompt).strip()

def confirm_action(message: str) -> bool:
    """Get user confirmation"""
    response = input(f"{message} (y/N): ").strip().lower()
    return response in ['y', 'yes']

def format_datetime(dt: Optional[datetime]) -> str:
    """Format datetime for display"""
    if dt is None:
        return "Never"
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def format_duration(minutes: Optional[int]) -> str:
    """Format duration in minutes to human readable format"""
    if minutes is None:
        return "N/A"
    
    hours = minutes // 60
    mins = minutes % 60
    
    if hours > 0:
        return f"{hours}h {mins}m"
    else:
        return f"{mins}m"

def get_local_ip() -> str:
    """Get local IP address"""
    try:
        # Connect to a remote server to get local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"

def validate_email(email: str) -> bool:
    """Basic email validation"""
    return "@" in email and "." in email.split("@")[1]

def validate_username(username: str) -> bool:
    """Validate username format"""
    return len(username) >= 3 and username.isalnum()

def print_error(message: str):
    """Print error message in red"""
    print(f"\033[91mError: {message}\033[0m")

def print_success(message: str):
    """Print success message in green"""
    print(f"\033[92mSuccess: {message}\033[0m")

def print_warning(message: str):
    """Print warning message in yellow"""
    print(f"\033[93mWarning: {message}\033[0m")

def print_info(message: str):
    """Print info message in blue"""
    print(f"\033[94mInfo: {message}\033[0m")

def pause():
    """Pause execution until user presses Enter"""
    input("\nPress Enter to continue...")

def print_table_header(headers: list):
    """Print table header"""
    header_line = " | ".join(f"{header:^15}" for header in headers)
    separator = "-" * len(header_line)
    print(separator)
    print(header_line)
    print(separator)

def print_table_row(values: list):
    """Print table row"""
    row_line = " | ".join(f"{str(value):^15}" for value in values)
    print(row_line)

def truncate_string(text: str, max_length: int = 15) -> str:
    """Truncate string to max length"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."
