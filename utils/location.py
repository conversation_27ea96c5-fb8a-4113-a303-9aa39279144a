"""
Location tracking utilities for the Online Register System
"""
import requests
import socket
from typing import Optional, Dict
import config

class LocationTracker:
    def __init__(self):
        self.enabled = config.ENABLE_LOCATION_TRACKING
    
    def get_location_info(self, ip_address: str = None) -> Dict[str, str]:
        """Get location information based on IP address"""
        if not self.enabled:
            return {"location": config.DEFAULT_LOCATION, "ip": "127.0.0.1"}
        
        if not ip_address:
            ip_address = self._get_public_ip()
        
        location = self._get_location_from_ip(ip_address)
        
        return {
            "location": location,
            "ip": ip_address
        }
    
    def _get_public_ip(self) -> str:
        """Get public IP address"""
        try:
            # Try multiple services for reliability
            services = [
                "https://api.ipify.org",
                "https://ipinfo.io/ip",
                "https://icanhazip.com"
            ]
            
            for service in services:
                try:
                    response = requests.get(service, timeout=5)
                    if response.status_code == 200:
                        return response.text.strip()
                except:
                    continue
            
            # Fallback to local IP
            return self._get_local_ip()
        except:
            return "127.0.0.1"
    
    def _get_local_ip(self) -> str:
        """Get local IP address"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except:
            return "127.0.0.1"
    
    def _get_location_from_ip(self, ip_address: str) -> str:
        """Get location from IP address using free geolocation service"""
        if ip_address in ["127.0.0.1", "localhost"]:
            return "Local Machine"
        
        try:
            # Use free ipinfo.io service
            response = requests.get(f"https://ipinfo.io/{ip_address}/json", timeout=5)
            if response.status_code == 200:
                data = response.json()
                city = data.get("city", "")
                region = data.get("region", "")
                country = data.get("country", "")
                
                location_parts = [part for part in [city, region, country] if part]
                if location_parts:
                    return ", ".join(location_parts)
        except:
            pass
        
        return config.DEFAULT_LOCATION
    
    def get_manual_location(self) -> str:
        """Get location manually from user input"""
        location = input("Enter your current location (or press Enter for default): ").strip()
        return location if location else config.DEFAULT_LOCATION
