"""
Location tracking utilities for the Online Register System
"""
import requests
import socket
import json
from typing import Optional, Dict, Tuple
import config

class LocationTracker:
    def __init__(self):
        self.enabled = config.ENABLE_LOCATION_TRACKING
        self.location_permission_granted = None

    def request_location_permission(self) -> bool:
        """Request user permission for location tracking"""
        if self.location_permission_granted is not None:
            return self.location_permission_granted

        print("\n" + "="*60)
        print("LOCATION SHARING REQUEST")
        print("="*60)
        print("The Student Register System would like to access your location")
        print("to track where you are studying for session logging purposes.")
        print("")
        print("This helps with:")
        print("• Accurate session location tracking")
        print("• Campus security and attendance")
        print("• Study pattern analysis")
        print("")
        print("Your location data will only be used for session tracking")
        print("and will not be shared with third parties.")
        print("="*60)

        while True:
            response = input("Allow location access? (y/n): ").strip().lower()
            if response in ['y', 'yes']:
                self.location_permission_granted = True
                print("✓ Location access granted")
                return True
            elif response in ['n', 'no']:
                self.location_permission_granted = False
                print("✗ Location access denied - using default location")
                return False
            else:
                print("Please enter 'y' for yes or 'n' for no")

    def get_location_info(self, ip_address: str = None) -> Dict[str, str]:
        """Get location information with user permission"""
        if not self.enabled:
            return {"location": config.DEFAULT_LOCATION, "ip": "127.0.0.1"}

        # Request permission if not already asked
        if not self.request_location_permission():
            return {"location": config.DEFAULT_LOCATION, "ip": self._get_local_ip()}

        if not ip_address:
            ip_address = self._get_public_ip()

        location = self._get_location_from_ip(ip_address)

        return {
            "location": location,
            "ip": ip_address
        }
    
    def _get_public_ip(self) -> str:
        """Get public IP address"""
        try:
            # Try multiple services for reliability
            services = [
                "https://api.ipify.org",
                "https://ipinfo.io/ip",
                "https://icanhazip.com"
            ]
            
            for service in services:
                try:
                    response = requests.get(service, timeout=5)
                    if response.status_code == 200:
                        return response.text.strip()
                except:
                    continue
            
            # Fallback to local IP
            return self._get_local_ip()
        except:
            return "127.0.0.1"
    
    def _get_local_ip(self) -> str:
        """Get local IP address"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except:
            return "127.0.0.1"
    
    def _get_location_from_ip(self, ip_address: str) -> str:
        """Get location from IP address using multiple geolocation services"""
        if ip_address in ["127.0.0.1", "localhost"]:
            return "Local Machine"

        # Try multiple services for better accuracy
        services = [
            f"https://ipinfo.io/{ip_address}/json",
            f"http://ip-api.com/json/{ip_address}",
            f"https://ipapi.co/{ip_address}/json/"
        ]

        for service_url in services:
            try:
                response = requests.get(service_url, timeout=5)
                if response.status_code == 200:
                    data = response.json()

                    # Handle different API response formats
                    if "ipinfo.io" in service_url:
                        city = data.get("city", "")
                        region = data.get("region", "")
                        country = data.get("country", "")
                    elif "ip-api.com" in service_url:
                        city = data.get("city", "")
                        region = data.get("regionName", "")
                        country = data.get("country", "")
                    elif "ipapi.co" in service_url:
                        city = data.get("city", "")
                        region = data.get("region", "")
                        country = data.get("country_name", "")

                    location_parts = [part for part in [city, region, country] if part]
                    if location_parts:
                        location = ", ".join(location_parts)
                        print(f"📍 Location detected: {location}")
                        return location
            except Exception as e:
                continue

        print(f"📍 Using default location: {config.DEFAULT_LOCATION}")
        return config.DEFAULT_LOCATION

    def get_precise_location(self) -> str:
        """Get more precise location using additional methods"""
        if not self.request_location_permission():
            return config.DEFAULT_LOCATION

        print("🔍 Detecting your location...")

        # Try to get location using multiple methods
        location_methods = [
            self._get_wifi_location,
            self._get_ip_geolocation,
            self._get_network_location
        ]

        for method in location_methods:
            try:
                location = method()
                if location and location != config.DEFAULT_LOCATION:
                    return location
            except Exception as e:
                continue

        return config.DEFAULT_LOCATION

    def _get_wifi_location(self) -> str:
        """Attempt to get location from WiFi networks (simulated)"""
        # In a real implementation, this would scan for WiFi networks
        # and use their MAC addresses to determine location
        # For demo purposes, we'll simulate this

        try:
            # Simulate WiFi network detection
            # In reality, you'd use libraries like wifi or subprocess to scan networks
            print("📶 Scanning WiFi networks...")

            # Simulated WeThinkCode WiFi networks
            simulated_networks = [
                "WeThinkCode_Campus_JHB",
                "WeThinkCode_Campus_CPT",
                "WeThinkCode_Student_WiFi",
                "WTC_Library",
                "WTC_Lab_1",
                "WTC_Lab_2"
            ]

            # Simulate finding a WeThinkCode network
            import random
            if random.random() > 0.3:  # 70% chance of finding WTC network
                network = random.choice(simulated_networks)
                print(f"📶 Found network: {network}")

                if "JHB" in network:
                    return "WeThinkCode Johannesburg Campus"
                elif "CPT" in network:
                    return "WeThinkCode Cape Town Campus"
                elif "Library" in network:
                    return "WeThinkCode Library"
                elif "Lab" in network:
                    return f"WeThinkCode {network.split('_')[-1]}"
                else:
                    return "WeThinkCode Campus"

        except Exception:
            pass

        return None

    def _get_ip_geolocation(self) -> str:
        """Get location from IP geolocation"""
        ip_address = self._get_public_ip()
        return self._get_location_from_ip(ip_address)

    def _get_network_location(self) -> str:
        """Get location from network information"""
        try:
            # Check if we're on a campus network by examining local IP ranges
            local_ip = self._get_local_ip()

            # Common WeThinkCode IP ranges (simulated)
            campus_networks = {
                "10.0.": "WeThinkCode Campus Network",
                "192.168.1.": "WeThinkCode Lab Network",
                "192.168.100.": "WeThinkCode Student Network",
                "172.16.": "WeThinkCode Administrative Network"
            }

            for network_prefix, location in campus_networks.items():
                if local_ip.startswith(network_prefix):
                    print(f"🌐 Detected campus network: {location}")
                    return location

        except Exception:
            pass

        return None
    
    def get_manual_location(self) -> str:
        """Get location manually from user input"""
        location = input("Enter your current location (or press Enter for default): ").strip()
        return location if location else config.DEFAULT_LOCATION
