#!/bin/bash

# WeThinkCode Student Register System Startup Script

echo "🎓 WeThinkCode Student Register System"
echo "======================================"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 and try again."
    exit 1
fi

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "📦 Installing dependencies..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies. Please check your internet connection and try again."
        exit 1
    fi
    echo "✅ Dependencies installed successfully!"
else
    echo "⚠️  requirements.txt not found. Assuming dependencies are already installed."
fi

# Create necessary directories
mkdir -p reports

echo ""
echo "🚀 Starting WeThinkCode Student Register System..."
echo "📍 Location tracking with user permission"
echo "📧 Email-based student registration"
echo "🔒 Enhanced security features"
echo ""
echo "Default admin credentials:"
echo "Username: admin"
echo "Password: admin123"
echo "⚠️  Please change the default password after first login!"
echo ""

# Start the application
python3 main.py
