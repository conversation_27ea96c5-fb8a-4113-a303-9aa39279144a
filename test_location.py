#!/usr/bin/env python3
"""
Test script to demonstrate location permission and detection
"""
from utils.location import LocationTracker

print("Testing Location Permission and Detection")
print("="*50)

# Create a fresh location tracker
tracker = LocationTracker()

# Test location permission request
print("1. Testing location permission request...")
location_info = tracker.get_location_info()
print(f"Location: {location_info['location']}")
print(f"IP: {location_info['ip']}")

print("\n2. Testing precise location detection...")
precise_location = tracker.get_precise_location()
print(f"Precise Location: {precise_location}")

print("\nLocation tracking test completed!")
