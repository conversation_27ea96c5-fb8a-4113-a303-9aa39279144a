# Online Student Register System

A comprehensive terminal-based authentication and session tracking system for students with advanced security features.

## Features

### Core Functionality
- **Admin-Only Registration**: Only administrators can register students using WeThinkCode emails
- **Automatic Temporary Passwords**: System generates and sends temporary passwords via email
- **Session Tracking**: Clock in/out functionality with automatic location detection
- **Location Permission System**: Users must grant permission for location tracking
- **Enhanced Location Detection**: Multiple methods including WiFi, IP geolocation, and network detection
- **Google Authenticator Integration**: TOTP-based two-factor authentication
- **Admin Reporting**: Comprehensive reporting and user management

### Security Features
- **Password Hashing**: bcrypt encryption for secure password storage
- **Account Lockout**: Protection against brute force attacks
- **Session Management**: Automatic session timeout and tracking
- **Location Tracking**: IP-based location detection for sessions
- **Security Logging**: Comprehensive audit trail of all activities

### User Types

#### Students
- Login with WeThinkCode email credentials
- Grant/deny location access permissions
- Start/end study sessions with automatic location tracking
- View session history and statistics
- Update profile and security settings
- Enable/disable two-factor authentication

#### Administrators
- All student capabilities
- Register students using WeThinkCode email addresses
- Manage user accounts (activate/deactivate)
- Reset user passwords and send temporary passwords
- Promote/demote users
- Generate comprehensive system reports
- Access security logs and analytics

## Installation

### Option 1: Quick Start (Local)
1. **Clone or download the project files**
2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Run the application**:
   ```bash
   python main.py
   # or use the startup script
   ./start.sh
   # or use the executable
   ./wethinkcode-register
   ```

### Option 2: System-Wide Installation
1. **Install system-wide** (requires sudo for /usr/local/bin):
   ```bash
   sudo ./install.sh
   ```
2. **Install for current user only**:
   ```bash
   ./install.sh
   ```
3. **Run from anywhere**:
   ```bash
   wethinkcode-register
   ```

### Uninstallation
```bash
./uninstall.sh
```

## First Time Setup

1. **Default Admin Account**:
   - Username: `admin`
   - Password: `admin123`
   - **Important**: Change this password immediately after first login!

2. **Configuration**:
   - Edit `config.py` to customize settings
   - Set environment variables for production use

## Usage

### For Students

1. **Getting Your Account**:
   - Your account is created by an administrator
   - You'll receive an email with your temporary password
   - Use your WeThinkCode email username to login

2. **First Login**:
   - Login with your username and temporary password
   - Change your password immediately for security
   - Set up two-factor authentication if desired

3. **Starting a Session**:
   - Login to your account
   - Select "Start Session"
   - Grant location access when prompted
   - Your session will begin with automatic location detection

4. **Location Tracking**:
   - System will request permission to access your location
   - Location is detected automatically using multiple methods:
     - WiFi network scanning for campus detection
     - IP-based geolocation
     - Network analysis for campus networks
   - You can grant or deny location access

5. **Ending a Session**:
   - Select "End Session" from the main menu
   - Your session duration will be calculated and saved

6. **Setting up 2FA**:
   - Go to "Security Settings"
   - Choose "Setup/Enable TOTP"
   - Scan the QR code with Google Authenticator
   - Enter the verification code to enable

### For Administrators

1. **Student Registration**:
   - Access "Register Student" from the admin menu
   - Enter student's WeThinkCode email address
   - System generates and sends temporary password automatically
   - Student receives email with login credentials

2. **User Management**:
   - Access "User Management" from the admin menu
   - View all users, activate/deactivate accounts
   - Reset passwords and change user roles
   - Promote students to admin or demote admins

3. **Reports**:
   - Generate user activity reports
   - Export session data with location information
   - View system dashboard with real-time statistics

4. **System Monitoring**:
   - Monitor active sessions with location data
   - Review security logs and authentication events
   - Track system usage patterns and location analytics

## File Structure

```
├── main.py                 # Main application entry point
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── auth/
│   ├── authenticator.py   # Authentication logic
│   └── totp_manager.py    # Google Authenticator integration
├── database/
│   ├── db_manager.py      # Database operations
│   └── models.py          # Data models
├── sessions/
│   └── session_manager.py # Session tracking
├── users/
│   └── user_manager.py    # User operations
├── reports/
│   └── report_generator.py # Reporting functionality
└── utils/
    ├── helpers.py         # Utility functions
    ├── location.py        # Enhanced location tracking
    └── email_service.py   # Email service for temporary passwords
```

## WeThinkCode Integration

This system is specifically designed for WeThinkCode campuses with:

### Email Integration
- **Student Emails**: Must end with `@student.wethinkcode.co.za`
- **Admin Emails**: Must end with `@wethinkcode.co.za`
- **Automatic Username**: Extracted from email (part before @)
- **Temporary Passwords**: Generated and sent via email simulation

### Location Detection
- **Campus WiFi Networks**: Detects WeThinkCode WiFi networks
- **Network Analysis**: Identifies campus IP ranges
- **Geographic Location**: IP-based location detection
- **Permission System**: Users must grant location access

### Campus-Specific Features
- **Default Locations**: WeThinkCode Campus, Library, Labs
- **Network Detection**: Automatic campus network identification
- **Multi-Campus Support**: Johannesburg and Cape Town campuses

## Configuration

Key settings in `config.py`:

- `DATABASE_PATH`: SQLite database file location
- `SESSION_TIMEOUT`: Session timeout in seconds
- `MAX_LOGIN_ATTEMPTS`: Maximum failed login attempts before lockout
- `ENABLE_LOCATION_TRACKING`: Enable/disable location tracking
- `WETHINKCODE_EMAIL_DOMAIN`: Student email domain
- `ADMIN_EMAIL_DOMAIN`: Admin email domain
- `MANUAL_LOCATION`: Enable/disable manual location entry
- `DEFAULT_ADMIN_*`: Default admin credentials

## Security Considerations

1. **Change Default Credentials**: Immediately change the default admin password
2. **Environment Variables**: Use environment variables for sensitive settings in production
3. **Database Security**: Secure the SQLite database file with appropriate permissions
4. **Network Security**: Use HTTPS if deploying over a network
5. **Regular Backups**: Implement regular database backups

## Database Schema

The system uses SQLite with the following main tables:

- **users**: User accounts and authentication data
- **sessions**: Session tracking and time logs
- **security_logs**: Security events and audit trail
- **password_resets**: Password reset tokens (future feature)

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed with `pip install -r requirements.txt`
2. **Database Errors**: Check file permissions for the database directory
3. **TOTP Issues**: Ensure system time is synchronized for TOTP verification
4. **Location Tracking**: Check internet connection for IP-based location services

### Logs

- Application logs are written to `register.log`
- Check logs for detailed error information
- Security events are logged to the database

## Development

### Adding Features

1. **New User Types**: Extend the `UserRole` enum in `models.py`
2. **Additional Reports**: Add methods to `ReportGenerator`
3. **Custom Authentication**: Extend the `Authenticator` class
4. **New Session Types**: Modify the `SessionManager` class

### Testing

The system includes comprehensive error handling and logging. Test thoroughly:

1. User registration and login flows
2. Session start/end functionality
3. Admin operations
4. TOTP setup and verification
5. Report generation

## License

This project is provided as-is for educational and internal use purposes.

## Support

For issues or questions:
1. Check the logs for error details
2. Review the configuration settings
3. Ensure all dependencies are properly installed
4. Verify database permissions and connectivity
