# Online Student Register System

A comprehensive terminal-based authentication and session tracking system for students with advanced security features.

## Features

### Core Functionality
- **User Registration & Login**: Secure account creation and authentication
- **Session Tracking**: Clock in/out functionality with location tracking
- **Password Recovery**: Secure password reset mechanism
- **Google Authenticator Integration**: TOTP-based two-factor authentication
- **Admin Reporting**: Comprehensive reporting and user management

### Security Features
- **Password Hashing**: bcrypt encryption for secure password storage
- **Account Lockout**: Protection against brute force attacks
- **Session Management**: Automatic session timeout and tracking
- **Location Tracking**: IP-based location detection for sessions
- **Security Logging**: Comprehensive audit trail of all activities

### User Types

#### Regular Users (Students)
- Register and manage accounts
- Login/logout with optional 2FA
- Start/end study sessions
- View session history and statistics
- Update profile and security settings

#### Administrators
- All regular user capabilities
- Manage user accounts (activate/deactivate)
- Reset user passwords
- Promote/demote users
- Generate system reports
- Access security logs and analytics

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

## First Time Setup

1. **Default Admin Account**:
   - Username: `admin`
   - Password: `admin123`
   - **Important**: Change this password immediately after first login!

2. **Configuration**:
   - Edit `config.py` to customize settings
   - Set environment variables for production use

## Usage

### For Students

1. **Registration**:
   - Choose "Register" from the main menu
   - Provide username, email, and password
   - Login with your new credentials

2. **Starting a Session**:
   - Login to your account
   - Select "Start Session"
   - Optionally enter your location manually
   - Your session will begin tracking time

3. **Ending a Session**:
   - Select "End Session" from the main menu
   - Your session duration will be calculated and saved

4. **Setting up 2FA**:
   - Go to "Security Settings"
   - Choose "Setup/Enable TOTP"
   - Scan the QR code with Google Authenticator
   - Enter the verification code to enable

### For Administrators

1. **User Management**:
   - Access "User Management" from the admin menu
   - View all users, activate/deactivate accounts
   - Reset passwords and change user roles

2. **Reports**:
   - Generate user activity reports
   - Export session data
   - View system dashboard with statistics

3. **System Monitoring**:
   - Monitor active sessions
   - Review security logs
   - Track system usage patterns

## File Structure

```
├── main.py                 # Main application entry point
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── auth/
│   ├── authenticator.py   # Authentication logic
│   └── totp_manager.py    # Google Authenticator integration
├── database/
│   ├── db_manager.py      # Database operations
│   └── models.py          # Data models
├── sessions/
│   └── session_manager.py # Session tracking
├── users/
│   └── user_manager.py    # User operations
├── reports/
│   └── report_generator.py # Reporting functionality
└── utils/
    ├── helpers.py         # Utility functions
    └── location.py        # Location tracking
```

## Configuration

Key settings in `config.py`:

- `DATABASE_PATH`: SQLite database file location
- `SESSION_TIMEOUT`: Session timeout in seconds
- `MAX_LOGIN_ATTEMPTS`: Maximum failed login attempts before lockout
- `ENABLE_LOCATION_TRACKING`: Enable/disable location tracking
- `DEFAULT_ADMIN_*`: Default admin credentials

## Security Considerations

1. **Change Default Credentials**: Immediately change the default admin password
2. **Environment Variables**: Use environment variables for sensitive settings in production
3. **Database Security**: Secure the SQLite database file with appropriate permissions
4. **Network Security**: Use HTTPS if deploying over a network
5. **Regular Backups**: Implement regular database backups

## Database Schema

The system uses SQLite with the following main tables:

- **users**: User accounts and authentication data
- **sessions**: Session tracking and time logs
- **security_logs**: Security events and audit trail
- **password_resets**: Password reset tokens (future feature)

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed with `pip install -r requirements.txt`
2. **Database Errors**: Check file permissions for the database directory
3. **TOTP Issues**: Ensure system time is synchronized for TOTP verification
4. **Location Tracking**: Check internet connection for IP-based location services

### Logs

- Application logs are written to `register.log`
- Check logs for detailed error information
- Security events are logged to the database

## Development

### Adding Features

1. **New User Types**: Extend the `UserRole` enum in `models.py`
2. **Additional Reports**: Add methods to `ReportGenerator`
3. **Custom Authentication**: Extend the `Authenticator` class
4. **New Session Types**: Modify the `SessionManager` class

### Testing

The system includes comprehensive error handling and logging. Test thoroughly:

1. User registration and login flows
2. Session start/end functionality
3. Admin operations
4. TOTP setup and verification
5. Report generation

## License

This project is provided as-is for educational and internal use purposes.

## Support

For issues or questions:
1. Check the logs for error details
2. Review the configuration settings
3. Ensure all dependencies are properly installed
4. Verify database permissions and connectivity
