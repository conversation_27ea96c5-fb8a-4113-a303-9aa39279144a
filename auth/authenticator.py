"""
Authentication system for the Online Register System
"""
import bcrypt
import secrets
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Op<PERSON>, <PERSON><PERSON>

from database.db_manager import DatabaseManager
from database.models import User, SecurityLog, UserRole
from auth.totp_manager import TOTPManager
from utils.email_service import EmailService
import config

class Authenticator:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.totp_manager = TOTPManager()
        self.email_service = EmailService()
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def register_student_by_admin(self, admin_user: User, student_email: str) -> Tuple[bool, str, str]:
        """Register a new student by admin with temporary password"""
        # Check if user is admin
        if admin_user.role != UserRole.ADMIN:
            return False, "Access denied: Admin privileges required", ""

        # Validate WeThinkCode email
        is_valid, message = self.email_service.validate_wethinkcode_email(student_email)
        if not is_valid:
            return False, message, ""

        # Extract username from email
        username = self.email_service.extract_username_from_email(student_email)

        # Check if user already exists
        if self.db.get_user_by_username(username):
            return False, "Student already registered", ""

        # Generate temporary password
        temp_password = self.email_service.generate_temporary_password()

        # Determine role based on email domain
        role = UserRole.ADMIN if self.email_service.is_admin_email(student_email) else UserRole.STUDENT

        # Create user
        user = User(
            username=username,
            email=student_email,
            password_hash=self.hash_password(temp_password),
            role=role,
            created_at=datetime.now()
        )

        user_id = self.db.create_user(user)
        if user_id:
            # Send temporary password via email
            email_success, email_message = self.email_service.send_temporary_password(
                student_email, temp_password, admin_user.username
            )

            self._log_security_event(user_id, username, "STUDENT_REGISTERED_BY_ADMIN", True,
                                   f"Registered by {admin_user.username}")

            if email_success:
                return True, f"Student {username} registered successfully. {email_message}", temp_password
            else:
                return True, f"Student {username} registered but email failed: {email_message}", temp_password
        else:
            return False, "Failed to create student account", ""

    def register_admin_user(self, username: str, email: str, password: str) -> Tuple[bool, str]:
        """Register admin user (for initial setup only)"""
        # Validate input
        if len(username) < 3:
            return False, "Username must be at least 3 characters long"

        if len(password) < 6:
            return False, "Password must be at least 6 characters long"

        # Validate admin email
        if not self.email_service.is_admin_email(email):
            return False, f"Admin email must end with {config.ADMIN_EMAIL_DOMAIN}"

        # Check if user exists
        if self.db.get_user_by_username(username):
            return False, "Username already exists"

        # Create admin user
        user = User(
            username=username,
            email=email,
            password_hash=self.hash_password(password),
            role=UserRole.ADMIN,
            created_at=datetime.now()
        )

        user_id = self.db.create_user(user)
        if user_id:
            self._log_security_event(user_id, username, "ADMIN_REGISTERED", True)
            return True, "Admin user registered successfully"
        else:
            return False, "Failed to create admin user"
    
    def login(self, username: str, password: str, totp_code: str = "", 
              location: str = "", ip_address: str = "") -> Tuple[bool, str, Optional[User]]:
        """Authenticate user login"""
        user = self.db.get_user_by_username(username)
        
        if not user:
            self._log_security_event(None, username, "LOGIN_FAILED", False, "User not found")
            return False, "Invalid username or password", None
        
        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.now():
            self._log_security_event(user.id, username, "LOGIN_BLOCKED", False, "Account locked")
            return False, "Account is temporarily locked", None
        
        # Check if account is active
        if not user.is_active:
            self._log_security_event(user.id, username, "LOGIN_FAILED", False, "Account disabled")
            return False, "Account is disabled", None
        
        # Verify password
        if not self.verify_password(password, user.password_hash):
            user.failed_login_attempts += 1
            
            # Lock account after max attempts
            if user.failed_login_attempts >= config.MAX_LOGIN_ATTEMPTS:
                user.locked_until = datetime.now() + timedelta(seconds=config.LOCKOUT_DURATION)
                self._log_security_event(user.id, username, "ACCOUNT_LOCKED", False, 
                                       f"Too many failed attempts")
            
            self.db.update_user(user)
            self._log_security_event(user.id, username, "LOGIN_FAILED", False, "Wrong password")
            return False, "Invalid username or password", None
        
        # Check TOTP if enabled
        if user.is_totp_enabled:
            if not totp_code:
                return False, "TOTP code required", None
            
            if not self.totp_manager.verify_totp(user.totp_secret, totp_code):
                user.failed_login_attempts += 1
                self.db.update_user(user)
                self._log_security_event(user.id, username, "LOGIN_FAILED", False, "Invalid TOTP")
                return False, "Invalid TOTP code", None
        
        # Successful login
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login = datetime.now()
        self.db.update_user(user)
        
        self._log_security_event(user.id, username, "LOGIN_SUCCESS", True, 
                               f"Location: {location}, IP: {ip_address}")
        
        return True, "Login successful", user
    
    def setup_totp(self, user: User) -> Tuple[str, str]:
        """Setup TOTP for user"""
        secret = self.totp_manager.generate_secret()
        qr_url = self.totp_manager.generate_qr_url(user.username, secret)
        
        user.totp_secret = secret
        self.db.update_user(user)
        
        self._log_security_event(user.id, user.username, "TOTP_SETUP", True)
        
        return secret, qr_url
    
    def enable_totp(self, user: User, totp_code: str) -> Tuple[bool, str]:
        """Enable TOTP after verification"""
        if not user.totp_secret:
            return False, "TOTP not set up"
        
        if self.totp_manager.verify_totp(user.totp_secret, totp_code):
            user.is_totp_enabled = True
            self.db.update_user(user)
            self._log_security_event(user.id, user.username, "TOTP_ENABLED", True)
            return True, "TOTP enabled successfully"
        else:
            return False, "Invalid TOTP code"
    
    def disable_totp(self, user: User, password: str) -> Tuple[bool, str]:
        """Disable TOTP after password verification"""
        if not self.verify_password(password, user.password_hash):
            return False, "Invalid password"
        
        user.is_totp_enabled = False
        user.totp_secret = None
        self.db.update_user(user)
        
        self._log_security_event(user.id, user.username, "TOTP_DISABLED", True)
        return True, "TOTP disabled successfully"
    
    def change_password(self, user: User, old_password: str, new_password: str) -> Tuple[bool, str]:
        """Change user password"""
        if not self.verify_password(old_password, user.password_hash):
            return False, "Current password is incorrect"
        
        if len(new_password) < 6:
            return False, "New password must be at least 6 characters long"
        
        user.password_hash = self.hash_password(new_password)
        self.db.update_user(user)
        
        self._log_security_event(user.id, user.username, "PASSWORD_CHANGED", True)
        return True, "Password changed successfully"
    
    def _log_security_event(self, user_id: Optional[int], username: str, action: str, 
                          success: bool, details: str = ""):
        """Log security event"""
        log = SecurityLog(
            user_id=user_id,
            username=username,
            action=action,
            success=success,
            details=details,
            timestamp=datetime.now()
        )
        self.db.log_security_event(log)
