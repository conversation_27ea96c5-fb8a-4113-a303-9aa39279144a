"""
TOTP (Time-based One-Time Password) manager for Google Authenticator integration
"""
import pyotp
import qrcode
import io
import base64
from typing import Optional
import config

class TOTPManager:
    def __init__(self):
        self.issuer = config.TOTP_ISSUER
    
    def generate_secret(self) -> str:
        """Generate a new TOTP secret"""
        return pyotp.random_base32()
    
    def generate_qr_url(self, username: str, secret: str) -> str:
        """Generate QR code URL for Google Authenticator"""
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            name=username,
            issuer_name=self.issuer
        )
        return provisioning_uri
    
    def generate_qr_code_ascii(self, username: str, secret: str) -> str:
        """Generate ASCII QR code for terminal display"""
        try:
            provisioning_uri = self.generate_qr_url(username, secret)
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=1,
                border=1,
            )
            qr.add_data(provisioning_uri)
            qr.make(fit=True)
            
            # Create ASCII representation
            matrix = qr.get_matrix()
            ascii_qr = ""
            for row in matrix:
                line = ""
                for cell in row:
                    line += "██" if cell else "  "
                ascii_qr += line + "\n"
            
            return ascii_qr
        except Exception:
            return "QR code generation failed. Please enter the secret manually in your authenticator app."
    
    def verify_totp(self, secret: str, token: str) -> bool:
        """Verify TOTP token"""
        if not secret or not token:
            return False
        
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=config.TOTP_VALIDITY_WINDOW)
        except Exception:
            return False
    
    def get_current_token(self, secret: str) -> Optional[str]:
        """Get current TOTP token (for testing purposes)"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.now()
        except Exception:
            return None
