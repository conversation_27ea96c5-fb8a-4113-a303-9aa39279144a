"""
Session management for the Online Register System
"""
from datetime import datetime
from typing import Optional, List, Tuple

from database.db_manager import DatabaseManager
from database.models import Session, User, SessionStatus
from utils.location import LocationTracker
from utils.helpers import get_local_ip
import config

class SessionManager:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.location_tracker = LocationTracker()
    
    def start_session(self, user: User) -> Tuple[bool, str]:
        """Start a new session for user with enhanced location detection"""
        # Check if user already has an active session
        active_session = self.db.get_active_session(user.id)
        if active_session:
            return False, "You already have an active session. Please end it first."

        # Get precise location information with user permission
        print("🔍 Determining your location for session tracking...")
        location = self.location_tracker.get_precise_location()

        # Get IP address
        location_info = self.location_tracker.get_location_info()
        ip_address = location_info["ip"]

        # Create new session
        session = Session(
            user_id=user.id,
            location=location,
            ip_address=ip_address,
            status=SessionStatus.ACTIVE,
            start_time=datetime.now()
        )

        session_id = self.db.create_session(session)
        if session_id:
            return True, f"✅ Session started successfully at {location}"
        else:
            return False, "❌ Failed to start session"
    
    def end_session(self, user: User) -> Tuple[bool, str]:
        """End active session for user"""
        active_session = self.db.get_active_session(user.id)
        if not active_session:
            return False, "No active session found"
        
        success = self.db.end_session(active_session.id)
        if success:
            # Get updated session info
            ended_session = self.db.get_active_session(user.id)
            if not ended_session:  # Session was successfully ended
                return True, "Session ended successfully"
        
        return False, "Failed to end session"
    
    def get_current_session(self, user: User) -> Optional[Session]:
        """Get current active session for user"""
        return self.db.get_active_session(user.id)
    
    def get_session_history(self, user: User, limit: int = 10) -> List[Session]:
        """Get session history for user"""
        return self.db.get_user_sessions(user.id, limit)
    
    def get_session_status(self, user: User) -> Tuple[bool, str, Optional[Session]]:
        """Get current session status"""
        active_session = self.db.get_active_session(user.id)
        
        if active_session:
            duration = self._calculate_current_duration(active_session)
            status_msg = f"Active session: {duration} at {active_session.location}"
            return True, status_msg, active_session
        else:
            return False, "No active session", None
    
    def _calculate_current_duration(self, session: Session) -> str:
        """Calculate current session duration"""
        if not session.start_time:
            return "Unknown"
        
        now = datetime.now()
        duration = now - session.start_time
        
        total_minutes = int(duration.total_seconds() / 60)
        hours = total_minutes // 60
        minutes = total_minutes % 60
        
        if hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
    
    def add_session_note(self, user: User, note: str) -> Tuple[bool, str]:
        """Add note to current active session"""
        active_session = self.db.get_active_session(user.id)
        if not active_session:
            return False, "No active session found"
        
        # Update session with note
        # Note: This would require adding an update_session method to DatabaseManager
        # For now, we'll return a placeholder
        return True, "Note added to session"
    
    def get_total_hours_today(self, user: User) -> float:
        """Get total hours worked today"""
        today = datetime.now().date()
        sessions = self.db.get_user_sessions(user.id, 50)  # Get recent sessions
        
        total_minutes = 0
        for session in sessions:
            if session.start_time and session.start_time.date() == today:
                if session.duration_minutes:
                    total_minutes += session.duration_minutes
                elif session.status == SessionStatus.ACTIVE:
                    # Calculate current duration for active session
                    current_duration = datetime.now() - session.start_time
                    total_minutes += int(current_duration.total_seconds() / 60)
        
        return total_minutes / 60.0  # Convert to hours
    
    def get_total_hours_week(self, user: User) -> float:
        """Get total hours worked this week"""
        # This is a simplified version - you might want to implement proper week calculation
        sessions = self.db.get_user_sessions(user.id, 100)
        
        total_minutes = 0
        for session in sessions:
            if session.duration_minutes:
                total_minutes += session.duration_minutes
        
        return total_minutes / 60.0
