"""
User management for the Online Register System
"""
from typing import List, Tuple, Optional
from datetime import datetime

from database.db_manager import DatabaseManager
from database.models import User, UserRole
from auth.authenticator import Authenticator
import config

class UserManager:
    def __init__(self, db_manager: DatabaseManager, authenticator: Authenticator):
        self.db = db_manager
        self.auth = authenticator
    
    def create_default_admin(self) -> bool:
        """Create default admin user if none exists"""
        admin_users = [user for user in self.db.get_all_users() if user.role == UserRole.ADMIN]
        
        if not admin_users:
            success, message = self.auth.register_user(
                config.DEFAULT_ADMIN_USERNAME,
                "<EMAIL>",
                config.DEFAULT_ADMIN_PASSWORD,
                UserRole.ADMIN
            )
            return success
        return True
    
    def get_user_profile(self, user: User) -> dict:
        """Get user profile information"""
        sessions = self.db.get_user_sessions(user.id, 5)  # Last 5 sessions
        
        # Calculate total sessions
        all_sessions = self.db.get_user_sessions(user.id, 1000)
        total_sessions = len(all_sessions)
        
        # Calculate total hours
        total_minutes = sum(s.duration_minutes or 0 for s in all_sessions)
        total_hours = total_minutes / 60.0
        
        return {
            "username": user.username,
            "email": user.email,
            "role": user.role.value.title(),
            "created_at": user.created_at,
            "last_login": user.last_login,
            "totp_enabled": user.is_totp_enabled,
            "total_sessions": total_sessions,
            "total_hours": total_hours,
            "recent_sessions": sessions
        }
    
    def update_user_email(self, user: User, new_email: str) -> Tuple[bool, str]:
        """Update user email"""
        if "@" not in new_email:
            return False, "Invalid email format"
        
        user.email = new_email
        success = self.db.update_user(user)
        
        if success:
            return True, "Email updated successfully"
        else:
            return False, "Failed to update email"
    
    def deactivate_user(self, admin_user: User, target_username: str) -> Tuple[bool, str]:
        """Deactivate a user account (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return False, "Access denied: Admin privileges required"
        
        target_user = self.db.get_user_by_username(target_username)
        if not target_user:
            return False, "User not found"
        
        if target_user.role == UserRole.ADMIN and target_user.id == admin_user.id:
            return False, "Cannot deactivate your own admin account"
        
        target_user.is_active = False
        success = self.db.update_user(target_user)
        
        if success:
            return True, f"User {target_username} deactivated successfully"
        else:
            return False, "Failed to deactivate user"
    
    def activate_user(self, admin_user: User, target_username: str) -> Tuple[bool, str]:
        """Activate a user account (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return False, "Access denied: Admin privileges required"
        
        target_user = self.db.get_user_by_username(target_username)
        if not target_user:
            return False, "User not found"
        
        target_user.is_active = True
        target_user.failed_login_attempts = 0
        target_user.locked_until = None
        success = self.db.update_user(target_user)
        
        if success:
            return True, f"User {target_username} activated successfully"
        else:
            return False, "Failed to activate user"
    
    def reset_user_password(self, admin_user: User, target_username: str, 
                           new_password: str) -> Tuple[bool, str]:
        """Reset user password (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return False, "Access denied: Admin privileges required"
        
        target_user = self.db.get_user_by_username(target_username)
        if not target_user:
            return False, "User not found"
        
        if len(new_password) < 6:
            return False, "Password must be at least 6 characters long"
        
        target_user.password_hash = self.auth.hash_password(new_password)
        target_user.failed_login_attempts = 0
        target_user.locked_until = None
        success = self.db.update_user(target_user)
        
        if success:
            return True, f"Password reset for user {target_username}"
        else:
            return False, "Failed to reset password"
    
    def promote_to_admin(self, admin_user: User, target_username: str) -> Tuple[bool, str]:
        """Promote user to admin (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return False, "Access denied: Admin privileges required"
        
        target_user = self.db.get_user_by_username(target_username)
        if not target_user:
            return False, "User not found"
        
        if target_user.role == UserRole.ADMIN:
            return False, "User is already an admin"
        
        target_user.role = UserRole.ADMIN
        success = self.db.update_user(target_user)
        
        if success:
            return True, f"User {target_username} promoted to admin"
        else:
            return False, "Failed to promote user"
    
    def demote_from_admin(self, admin_user: User, target_username: str) -> Tuple[bool, str]:
        """Demote admin to regular user (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return False, "Access denied: Admin privileges required"
        
        target_user = self.db.get_user_by_username(target_username)
        if not target_user:
            return False, "User not found"
        
        if target_user.id == admin_user.id:
            return False, "Cannot demote yourself"
        
        if target_user.role != UserRole.ADMIN:
            return False, "User is not an admin"
        
        target_user.role = UserRole.STUDENT
        success = self.db.update_user(target_user)
        
        if success:
            return True, f"User {target_username} demoted to regular user"
        else:
            return False, "Failed to demote user"
    
    def list_all_users(self, admin_user: User) -> List[User]:
        """List all users (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return []
        
        return self.db.get_all_users()
    
    def get_user_statistics(self, admin_user: User) -> dict:
        """Get user statistics (admin only)"""
        if admin_user.role != UserRole.ADMIN:
            return {}
        
        users = self.db.get_all_users()
        active_users = [u for u in users if u.is_active]
        admin_users = [u for u in users if u.role == UserRole.ADMIN]
        
        return {
            "total_users": len(users),
            "active_users": len(active_users),
            "inactive_users": len(users) - len(active_users),
            "admin_users": len(admin_users),
            "student_users": len(users) - len(admin_users)
        }
