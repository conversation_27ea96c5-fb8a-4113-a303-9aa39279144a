#!/bin/bash

# WeThinkCode Student Register System Uninstallation Script

echo "🗑️  WeThinkCode Student Register System - Uninstallation"
echo "======================================================="

PROJECT_NAME="wethinkcode-register"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    INSTALL_DIR="/usr/local/bin"
    echo "🔍 Checking system-wide installation in $INSTALL_DIR"
else
    INSTALL_DIR="$HOME/.local/bin"
    echo "🔍 Checking user installation in $INSTALL_DIR"
fi

EXECUTABLE_PATH="$INSTALL_DIR/$PROJECT_NAME"
APP_DIR="$INSTALL_DIR/wethinkcode-register-app"

# Check if installed
if [ ! -f "$EXECUTABLE_PATH" ]; then
    echo "❌ WeThinkCode Student Register System is not installed"
    echo "   Looked for: $EXECUTABLE_PATH"
    exit 1
fi

echo "📍 Found installation:"
echo "  Executable: $EXECUTABLE_PATH"
echo "  App files: $APP_DIR"

# Confirm uninstallation
echo ""
read -p "❓ Are you sure you want to uninstall? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Uninstallation cancelled"
    exit 0
fi

# Remove files
echo "🗑️  Removing files..."

if [ -f "$EXECUTABLE_PATH" ]; then
    rm "$EXECUTABLE_PATH"
    echo "  ✅ Removed executable: $EXECUTABLE_PATH"
else
    echo "  ⚠️  Executable not found: $EXECUTABLE_PATH"
fi

if [ -d "$APP_DIR" ]; then
    rm -rf "$APP_DIR"
    echo "  ✅ Removed application directory: $APP_DIR"
else
    echo "  ⚠️  Application directory not found: $APP_DIR"
fi

# Check if completely removed
if [ ! -f "$EXECUTABLE_PATH" ] && [ ! -d "$APP_DIR" ]; then
    echo ""
    echo "✅ WeThinkCode Student Register System successfully uninstalled!"
    echo ""
    echo "📝 Note: Python dependencies were not removed"
    echo "   To remove them manually, run:"
    echo "   pip3 uninstall bcrypt pyotp qrcode requests tabulate colorama cryptography pillow"
else
    echo ""
    echo "❌ Uninstallation may not be complete"
    echo "   Please manually remove any remaining files"
fi
