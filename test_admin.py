#!/usr/bin/env python3
"""
Test script to debug admin creation
"""
from database.db_manager import DatabaseManager
from auth.authenticator import Authenticator
from users.user_manager import UserManager
from database.models import UserRole

# Initialize components
db = DatabaseManager()
auth = Authenticator(db)
user_manager = UserManager(db, auth)

# Check existing users
users = db.get_all_users()
print(f"Total users: {len(users)}")

for user in users:
    print(f"User: {user.username}, Email: {user.email}, Role: {user.role.value}")

# Try to create default admin
print("\nTrying to create default admin...")
result = user_manager.create_default_admin()
print(f"Result: {result}")

# Check users again
users = db.get_all_users()
print(f"\nTotal users after creation: {len(users)}")

for user in users:
    print(f"User: {user.username}, Email: {user.email}, Role: {user.role.value}")

# Test login
print("\nTesting admin login...")
success, message, user = auth.login("admin", "admin123")
print(f"Login result: {success}, Message: {message}")
if user:
    print(f"Logged in user: {user.username}, Role: {user.role.value}")
