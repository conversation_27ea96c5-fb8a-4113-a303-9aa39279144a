"""
Configuration settings for the Online Register System
"""
import os
from pathlib import Path

# Database configuration
DATABASE_PATH = "student_register.db"

# Security settings
SECRET_KEY = os.environ.get('SECRET_KEY', 'your-secret-key-change-this')
SESSION_TIMEOUT = 3600  # 1 hour in seconds
MAX_LOGIN_ATTEMPTS = 3
LOCKOUT_DURATION = 300  # 5 minutes in seconds

# TOTP settings
TOTP_ISSUER = "Student Register System"
TOTP_VALIDITY_WINDOW = 1

# Application settings
APP_NAME = "Online Student Register"
APP_VERSION = "1.0.0"

# Location tracking
ENABLE_LOCATION_TRACKING = True
DEFAULT_LOCATION = "Unknown"

# Logging
LOG_LEVEL = "INFO"
LOG_FILE = "register.log"

# Admin settings
DEFAULT_ADMIN_USERNAME = "mphomofokeng"
DEFAULT_ADMIN_PASSWORD = "admin123"  # Should be changed on first run

# Report settings
REPORTS_DIR = "reports"
MAX_REPORT_RECORDS = 1000

# Terminal UI settings
CLEAR_SCREEN = True
SHOW_BANNER = True
